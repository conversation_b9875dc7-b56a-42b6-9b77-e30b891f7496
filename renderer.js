const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 全局变量
let reminders = [];
let currentReminderId = null;

// 窗口控制函数
function minimizeWindow() {
    ipcRenderer.send('minimize-window');
}

function closeWindow() {
    ipcRenderer.send('close-window');
}

// 初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    setupSliders();
    await loadReminders();
    renderReminders();
    startReminderChecker();
});

// 设置滑动条
function setupSliders() {
    const daySlider = document.getElementById('daySlider');
    const hourSlider = document.getElementById('hourSlider');
    const minuteSlider = document.getElementById('minuteSlider');
    
    const dayValue = document.getElementById('dayValue');
    const hourValue = document.getElementById('hourValue');
    const minuteValue = document.getElementById('minuteValue');
    const selectedTime = document.getElementById('selectedTime');

    const updateTime = () => {
        const days = parseInt(daySlider.value);
        const hours = parseInt(hourSlider.value);
        const minutes = parseInt(minuteSlider.value);

        dayValue.textContent = days === 0 ? '今天' : `${days}天后`;
        hourValue.textContent = hours.toString().padStart(2, '0');
        minuteValue.textContent = minutes.toString().padStart(2, '0');

        let timeStr;
        if (days === 0) {
            timeStr = `今天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        } else if (days === 1) {
            timeStr = `明天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        } else {
            timeStr = `${days}天后 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }
        selectedTime.textContent = timeStr;
    };

    daySlider.addEventListener('input', updateTime);
    hourSlider.addEventListener('input', updateTime);
    minuteSlider.addEventListener('input', updateTime);

    updateTime();
}

// 添加提醒
async function addReminder() {
    const input = document.getElementById('taskInput');
    const daySlider = document.getElementById('daySlider');
    const hourSlider = document.getElementById('hourSlider');
    const minuteSlider = document.getElementById('minuteSlider');
    
    const text = input.value.trim();
    if (!text) {
        alert('请输入提醒内容');
        return;
    }

    const days = parseInt(daySlider.value);
    const hours = parseInt(hourSlider.value);
    const minutes = parseInt(minuteSlider.value);

    // 计算提醒时间
    const now = new Date();
    const reminderTime = new Date(now);
    reminderTime.setDate(now.getDate() + days);
    reminderTime.setHours(hours, minutes, 0, 0);

    const reminder = {
        id: Date.now().toString(),
        text: text,
        time: reminderTime.toISOString(),
        completed: false,
        created: new Date().toISOString()
    };

    reminders.push(reminder);
    await saveReminders();
    renderReminders();
    
    // 清空输入
    input.value = '';
    
    // 重置滑动条
    daySlider.value = 0;
    hourSlider.value = 12;
    minuteSlider.value = 0;
    setupSliders(); // 更新显示
}

// 删除提醒
async function deleteReminder(id) {
    reminders = reminders.filter(r => r.id !== id);
    await saveReminders();
    renderReminders();
}

// 完成提醒
async function completeReminder(id) {
    const reminder = reminders.find(r => r.id === id);
    if (reminder) {
        reminder.completed = true;
        await saveReminders();
        renderReminders();
    }
}

// 渲染提醒列表
function renderReminders() {
    const grid = document.getElementById('remindersGrid');
    
    if (reminders.length === 0) {
        grid.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📝</div>
                <h3>还没有提醒</h3>
                <p>添加你的第一个提醒吧！</p>
            </div>
        `;
        return;
    }

    // 按时间排序
    const sortedReminders = [...reminders].sort((a, b) => new Date(a.time) - new Date(b.time));
    
    grid.innerHTML = sortedReminders.map((reminder, index) => {
        const reminderTime = new Date(reminder.time);
        const now = new Date();
        const isExpired = reminderTime < now && !reminder.completed;
        
        let timeDisplay = formatTimeDisplay(reminderTime, now);
        
        const colorClass = `color-${(index % 6) + 1}`;
        
        return `
            <div class="reminder-card ${colorClass} ${reminder.completed ? 'completed' : ''}" data-id="${reminder.id}">
                <div class="reminder-title">${reminder.text}</div>
                <div class="reminder-time ${isExpired ? 'expired' : ''}">${timeDisplay}</div>
                <div class="reminder-actions">
                    ${!reminder.completed ? `<button class="reminder-btn" onclick="completeReminder('${reminder.id}')">完成</button>` : ''}
                    <button class="reminder-btn" onclick="deleteReminder('${reminder.id}')">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 格式化时间显示
function formatTimeDisplay(reminderTime, now) {
    const diff = reminderTime - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diff < 0) {
        return '已过期';
    } else if (days > 0) {
        return `${days}天后 ${reminderTime.getHours().toString().padStart(2, '0')}:${reminderTime.getMinutes().toString().padStart(2, '0')}`;
    } else if (hours > 0) {
        return `${hours}小时后`;
    } else if (minutes > 0) {
        return `${minutes}分钟后`;
    } else {
        return '即将到时';
    }
}

// 保存提醒到本地
async function saveReminders() {
    try {
        await ipcRenderer.invoke('save-reminders', reminders);
    } catch (error) {
        console.error('保存提醒失败:', error);
    }
}

// 从本地加载提醒
async function loadReminders() {
    try {
        reminders = await ipcRenderer.invoke('load-reminders') || [];
    } catch (error) {
        console.error('加载提醒失败:', error);
        reminders = [];
    }
}

// 检查提醒时间
function startReminderChecker() {
    setInterval(async () => {
        const now = new Date();
        
        for (const reminder of reminders) {
            if (!reminder.completed && !reminder.notified) {
                const reminderTime = new Date(reminder.time);
                
                if (now >= reminderTime) {
                    // 显示系统通知
                    await ipcRenderer.invoke('show-notification', '提醒时间到了！', reminder.text);
                    
                    // 标记为已通知
                    reminder.notified = true;
                    await saveReminders();
                }
            }
        }
        
        // 更新显示
        renderReminders();
    }, 30000); // 每30秒检查一次
}

// 快捷键支持
document.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && e.target.id === 'taskInput') {
        addReminder();
    }
});

// 聚焦输入框
function focusInput() {
    document.getElementById('taskInput').focus();
}
