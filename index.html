<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happi Reminder - 桌面应用</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 自定义标题栏 -->
    <div class="titlebar">
        <div class="titlebar-drag">
            <i class="fas fa-bell"></i>
            <span>Happi Reminder</span>
        </div>
        <div class="titlebar-controls">
            <button class="control-btn minimize-btn" onclick="minimizeWindow()">
                <i class="fas fa-minus"></i>
            </button>
            <button class="control-btn close-btn" onclick="closeWindow()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="container">
        <!-- 添加提醒表单 -->
        <section class="add-reminder-section">
            <div class="add-reminder-form">
                <h2>添加新提醒</h2>
                <div class="form-group">
                    <input type="text" id="taskInput" placeholder="输入你的待办事项..." maxlength="100">
                </div>

                <!-- 时间滑动选择器 -->
                <div class="time-slider-container">
                    <h3>选择提醒时间</h3>
                    <div class="time-sliders">
                        <div class="slider-group">
                            <label>天数 (今天起)</label>
                            <input type="range" id="daySlider" min="0" max="10" value="0" class="slider">
                            <span id="dayValue">今天</span>
                        </div>
                        <div class="slider-group">
                            <label>小时</label>
                            <input type="range" id="hourSlider" min="0" max="23" value="12" class="slider">
                            <span id="hourValue">12</span>
                        </div>
                        <div class="slider-group">
                            <label>分钟</label>
                            <input type="range" id="minuteSlider" min="0" max="59" value="0" step="5" class="slider">
                            <span id="minuteValue">00</span>
                        </div>
                    </div>
                    <div class="selected-time">
                        <i class="fas fa-clock"></i>
                        <span id="selectedTime">今天 12:00</span>
                    </div>
                </div>

                <button id="addBtn" class="btn btn-primary" onclick="addReminder()">
                    <i class="fas fa-bell"></i> 添加提醒
                </button>
            </div>
        </section>

        <!-- 提醒列表 -->
        <section class="reminders-section">
            <h2>我的提醒</h2>
            <div class="reminders-grid" id="remindersGrid">
                <!-- 动态生成的提醒卡片 -->
            </div>
        </section>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
