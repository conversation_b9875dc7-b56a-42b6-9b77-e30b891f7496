const { app, BrowserWindow, <PERSON>u, Tray, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// 保持对窗口对象的全局引用
let mainWindow;
let tray = null;

// 数据存储路径
const userDataPath = app.getPath('userData');
const dataFilePath = path.join(userDataPath, 'reminders.json');

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 450,
    height: 700,
    minWidth: 400,
    minHeight: 600,
    maxWidth: 600,
    maxHeight: 900,
    frame: false, // 无边框窗口
    transparent: false,
    alwaysOnTop: true, // 始终置顶
    resizable: true,
    movable: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false // 先不显示，等加载完成后再显示
  });

  // 加载应用的 index.html
  mainWindow.loadFile('index.html');

  // 当窗口准备好时显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // 开发模式下打开开发者工具
    if (process.argv.includes('--dev')) {
      mainWindow.webContents.openDevTools();
    }
  });

  // 当窗口被关闭时，隐藏到系统托盘而不是退出
  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
  });

  // 窗口失去焦点时的处理（可选：自动隐藏）
  mainWindow.on('blur', () => {
    // 可以选择在失去焦点时隐藏窗口
    // mainWindow.hide();
  });
}

function createTray() {
  // 创建系统托盘 - 使用简单的图标路径
  let iconPath;
  if (process.platform === 'win32') {
    iconPath = path.join(__dirname, 'assets/icon.ico');
  } else {
    iconPath = path.join(__dirname, 'assets/icon.png');
  }

  // 如果图标文件不存在，创建一个简单的图标
  if (!fs.existsSync(iconPath)) {
    // 暂时跳过托盘图标，稍后添加
    console.log('托盘图标文件不存在，跳过托盘创建');
    return;
  }

  tray = new Tray(iconPath);
  
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示便签',
      click: () => {
        mainWindow.show();
        mainWindow.focus();
      }
    },
    {
      label: '隐藏便签',
      click: () => {
        mainWindow.hide();
      }
    },
    { type: 'separator' },
    {
      label: '关于',
      click: () => {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: '关于 Happi Reminder',
          message: 'Happi Reminder v1.0.0',
          detail: '一个简单易用的桌面便签提醒应用'
        });
      }
    },
    {
      label: '退出',
      click: () => {
        app.isQuiting = true;
        app.quit();
      }
    }
  ]);
  
  tray.setToolTip('Happi Reminder - 桌面便签');
  tray.setContextMenu(contextMenu);
  
  // 双击托盘图标显示/隐藏窗口
  tray.on('double-click', () => {
    if (mainWindow.isVisible()) {
      mainWindow.hide();
    } else {
      mainWindow.show();
      mainWindow.focus();
    }
  });
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  createWindow();
  createTray();

  app.on('activate', () => {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      mainWindow.show();
    }
  });
});

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 通信处理
ipcMain.handle('save-reminders', async (event, reminders) => {
  try {
    fs.writeFileSync(dataFilePath, JSON.stringify(reminders, null, 2));
    return { success: true };
  } catch (error) {
    console.error('保存提醒失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('load-reminders', async () => {
  try {
    if (fs.existsSync(dataFilePath)) {
      const data = fs.readFileSync(dataFilePath, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('加载提醒失败:', error);
    return [];
  }
});

// 显示系统通知
ipcMain.handle('show-notification', async (event, title, body) => {
  const { Notification } = require('electron');

  if (Notification.isSupported()) {
    const notification = new Notification({
      title: title,
      body: body
    });

    notification.show();

    notification.on('click', () => {
      mainWindow.show();
      mainWindow.focus();
    });
  }
});

// 窗口控制IPC
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.on('close-window', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});
